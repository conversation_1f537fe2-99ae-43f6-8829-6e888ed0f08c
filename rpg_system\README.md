# RPG系統

基於現有Gacha系統的完整RPG戰鬥系統實現。

## 項目狀態

### ✅ 已完成（第一階段）

1. **基礎架構搭建**
   - 創建了完整的目錄結構
   - 實現了配置加載器 (`config/loader.py`)
   - 實現了公式求值引擎 (`formula_engine/evaluator.py`)
     - ✅ 使用 `asteval` 庫進行安全的公式求值
     - ✅ 豐富的數學函數支持 (min, max, abs, round, floor, ceil, sqrt, pow, ln, log10)
     - ✅ 邏輯函數支持 (iff, clamp, log)
     - ✅ 數學常量支持 (PI, E)
     - ✅ 點號變量名自動轉換 (caster.stat.patk → caster_stat_patk)
     - ✅ 完善的錯誤處理和安全性保護

2. **數據庫擴展**
   - 為 `gacha_user_collections` 表添加了RPG相關字段
   - 創建了 `gacha_user_learned_global_skills` 表
   - 創建了 `rpg_user_progress` 表
   - 提供了完整的數據庫遷移腳本

3. **核心領域模型**
   - 實現了 `Battle` 戰鬥實例模型
   - 實現了 `Combatant` 戰鬥單位模型
   - 實現了 `SkillInstance` 技能實例模型
   - 實現了 `StatusEffectInstance` 狀態效果實例模型
   - 實現了 `BattleLogEntry` 戰鬥日誌模型

4. **配置系統**
   - 實現了基礎的Pydantic驗證模型
   - 創建了示例配置文件（效果模板、主動技能、狀態效果）
   - 實現了配置驗證和加載機制

5. **數據訪問層**
   - 實現了 `PlayerCollectionRpgRepository` 基礎功能

### ✅ 已完成（第二階段）

1. **戰鬥系統核心邏輯**
   - ✅ 效果應用器 (`EffectApplier`) - 完整實現，支持所有主要效果類型
   - ✅ 目標選擇器 (`TargetSelector`) - 完整實現，支持複雜目標選擇邏輯
   - ✅ 傷害處理器 (`DamageHandler`) - 完整實現，包含暴擊、命中、防禦計算
   - ✅ 被動觸發器 (`PassiveTriggerHandler`) - 完整實現，支持事件驅動的被動觸發

2. **戰鬥模型擴展**
   - ✅ 為 `Battle` 模型添加了目標選擇所需的方法
   - ✅ 完善了戰鬥單位間的交互邏輯

### 🚧 進行中（第三階段）

1. **屬性計算系統**
   - 屬性計算器 (`AttributeCalculator`)
   - 戰鬥屬性計算邏輯

### 📋 待完成

1. **配置文件完善**
   - 完整的技能配置文件
   - 卡牌RPG配置生成
   - 怪物和關卡配置

2. **戰鬥引擎**
   - 完整的戰鬥流程實現
   - AI決策邏輯
   - 戰鬥結果處理

3. **應用服務層**
   - `BattleCoordinatorService`
   - `PlayerCardManagementService`
   - `PlayerSkillManagementService`

4. **用戶界面**
   - Discord命令實現
   - 戰鬥結果展示
   - 卡牌管理界面

## 目錄結構

```
rpg_system/
├── battle_system/          # 戰鬥系統核心
│   ├── models/            # 領域模型
│   ├── handlers/          # 處理器
│   └── services/          # 戰鬥相關服務
├── config/                # 配置系統
│   ├── pydantic_models/   # 驗證模型
│   ├── data/             # JSON配置文件
│   └── loader.py         # 配置加載器
├── formula_engine/        # 公式求值引擎
├── repositories/          # 數據訪問層
├── services/             # 應用服務層
├── cogs/                 # Discord命令
├── views/                # 視圖層
└── utils/                # 工具函數
```

## 使用方法

### 測試配置加載器

```bash
cd rpg_system
python test_config_loader.py
```

### 測試效果處理器

```bash
cd rpg_system
python test_effect_handlers.py
```

### 測試 asteval 公式求值器

```bash
cd rpg_system
python test_asteval_formula.py
```

### 數據庫遷移

執行以下SQL腳本來添加RPG相關的數據庫結構：

```sql
-- 執行遷移腳本
\i database/postgresql/migrations/001_add_rpg_fields.sql
```

### 配置文件

配置文件位於 `rpg_system/config/data/` 目錄下：

- `effect_templates.json` - 效果模板定義
- `active_skills.json` - 主動技能定義
- `status_effects.json` - 狀態效果定義

## 設計原則

1. **數據驅動** - 遊戲邏輯通過JSON配置文件驅動
2. **純異常模式** - 遵循現有代碼庫的錯誤處理模式
3. **分層架構** - 清晰的職責分離
4. **可擴展性** - 易於添加新功能和內容
5. **性能優化** - 配置文件在啟動時加載到內存

## 下一步計劃

1. ✅ ~~完成戰鬥系統的核心處理器實現~~ (已完成)
2. 實現屬性計算器 (`AttributeCalculator`)
3. 完善戰鬥流程實現 (`Battle.process_action` 等方法)
4. 創建卡牌RPG配置生成腳本
5. 實現應用服務層 (`BattleCoordinatorService` 等)
6. 實現Discord命令接口
7. 添加完整的測試覆蓋

## 🎉 第二階段完成總結

✅ **核心戰鬥處理器全部實現完成！**

- **EffectApplier**: 效果系統的核心協調者，支持傷害、治療、狀態效果、屬性修改、MP操作等所有主要效果類型
- **TargetSelector**: 智能目標選擇器，支持複雜的目標選擇邏輯、條件過濾、排序和選擇策略
- **DamageHandler**: 完整的傷害計算系統，包含暴擊、命中/閃避、防禦減免、修正器等機制
- **PassiveTriggerHandler**: 事件驅動的被動技能觸發系統，支持條件檢查和機率計算

🧪 **測試驗證**: 所有處理器都通過了基礎功能測試，確保核心邏輯正確運行

📈 **架構優勢**:
- 高度模組化設計，各處理器職責清晰
- 完全數據驅動，通過JSON配置控制遊戲邏輯
- 集成公式求值引擎，支持複雜的數值計算
- 遵循純異常模式錯誤處理

## 相關文檔

詳細的設計文檔位於 `docs/RPG/` 目錄下：

- `RPG_00_Intro_Core_Philosophy.md` - 核心設計哲學
- `RPG_01_System_Architecture.md` - 系統架構
- `RPG_02_Configuration_Files.md` - 配置文件結構
- `RPG_03_Database_Schema.md` - 數據庫設計
- `RPG_04_Domain_Model_Battle_System.md` - 領域模型設計
