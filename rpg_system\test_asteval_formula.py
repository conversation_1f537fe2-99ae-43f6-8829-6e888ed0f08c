"""
測試使用 asteval 的新公式求值器
"""

import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rpg_system.formula_engine.evaluator import FormulaEvaluator
from utils.logger import logger


def test_asteval_formula_evaluator():
    """測試 asteval 公式求值器"""
    
    print("🧪 測試 asteval 公式求值器...")
    
    evaluator = FormulaEvaluator()
    
    # 測試基本數學運算
    print("\n📊 測試基本數學運算:")
    test_cases = [
        ("2 + 3", {}, 5.0),
        ("10 * 2.5", {}, 25.0),
        ("100 / 4", {}, 25.0),
        ("2 ** 3", {}, 8.0),
        ("(5 + 3) * 2", {}, 16.0),
    ]
    
    for formula, context, expected in test_cases:
        result = evaluator.evaluate(formula, context)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"   {status} {formula} = {result} (期望: {expected})")
    
    # 測試變量替換
    print("\n🔢 測試變量替換:")
    context = {
        'caster_stat_patk': 150,
        'target_stat_pdef': 80,
        'skill_level': 3,
        'caster_current_hp_percent': 0.75
    }
    
    variable_tests = [
        ("caster_stat_patk * 1.5", 225.0),
        ("caster_stat_patk - target_stat_pdef", 70.0),
        ("skill_level * 10 + 50", 80.0),
        ("caster_current_hp_percent * 100", 75.0),
    ]
    
    for formula, expected in variable_tests:
        result = evaluator.evaluate(formula, context)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"   {status} {formula} = {result} (期望: {expected})")
    
    # 測試數學函數
    print("\n🔬 測試數學函數:")
    math_tests = [
        ("min(10, 5)", {}, 5.0),
        ("max(10, 5)", {}, 10.0),
        ("abs(-15)", {}, 15.0),
        ("round(3.7)", {}, 4.0),
        ("floor(3.9)", {}, 3.0),
        ("ceil(3.1)", {}, 4.0),
        ("sqrt(16)", {}, 4.0),
        ("pow(2, 3)", {}, 8.0),
    ]
    
    for formula, context, expected in math_tests:
        result = evaluator.evaluate(formula, context)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"   {status} {formula} = {result} (期望: {expected})")
    
    # 測試邏輯函數
    print("\n🧠 測試邏輯函數:")
    logic_context = {
        'hp_percent': 0.3,
        'enemy_count': 2,
        'skill_level': 5
    }
    
    logic_tests = [
        ("iff(hp_percent < 0.5, 100, 50)", 100.0),  # 使用 iff 而不是 if
        ("iff(enemy_count > 1, skill_level * 20, skill_level * 10)", 100.0),
        ("clamp(skill_level * 30, 50, 200)", 150.0),
        ("clamp(skill_level * 5, 50, 200)", 50.0),
    ]
    
    for formula, expected in logic_tests:
        result = evaluator.evaluate(formula, logic_context)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"   {status} {formula} = {result} (期望: {expected})")
    
    # 測試點號變量名處理
    print("\n🔗 測試點號變量名處理:")
    dot_context = {
        'caster.stat.patk': 200,
        'target.stat.pdef': 100,
        'battle.current_turn': 5
    }
    
    dot_tests = [
        ("caster_stat_patk * 2", 400.0),  # 點號被替換為下劃線
        ("target_stat_pdef + 50", 150.0),
        ("battle_current_turn * 10", 50.0),
    ]
    
    for formula, expected in dot_tests:
        result = evaluator.evaluate(formula, dot_context)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"   {status} {formula} = {result} (期望: {expected})")
    
    # 測試安全性（應該安全地處理錯誤）
    print("\n🛡️ 測試安全性:")
    safety_tests = [
        ("undefined_variable", {}),
        ("1 / 0", {}),  # 除零錯誤
        ("sqrt(-1)", {}),  # 數學錯誤
        ("", {}),  # 空公式
        ("invalid syntax +++", {}),  # 語法錯誤
    ]
    
    for formula, context in safety_tests:
        result = evaluator.evaluate(formula, context)
        print(f"   🛡️ {formula or '(空公式)'} = {result} (應該安全返回 0.0)")
    
    # 測試常量
    print("\n🔢 測試常量:")
    constant_tests = [
        ("PI", {}, 3.14159),
        ("E", {}, 2.71828),
        ("PI * 2", {}, 6.28318),
    ]
    
    for formula, context, expected in constant_tests:
        result = evaluator.evaluate(formula, context)
        status = "✅" if abs(result - expected) < 0.01 else "❌"  # 較寬鬆的精度
        print(f"   {status} {formula} = {result} (期望: ~{expected})")
    
    print("\n✅ asteval 公式求值器測試完成！")


if __name__ == "__main__":
    test_asteval_formula_evaluator()
