#!/usr/bin/env python3
"""
測試戰鬥系統的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from battle_system.models.battle import Battle, BattleStatus
from battle_system.models.combatant import Combatant
from battle_system.models.skill_instance import SkillInstance, SkillType
from battle_system.services.attribute_calculator import AttributeCalculator
from config.loader import <PERSON>fig<PERSON><PERSON><PERSON>


def create_test_combatant(name: str, is_player: bool = True, hp: int = 100, mp: int = 50) -> Combatant:
    """創建測試用戰鬥單位"""
    combatant = Combatant(
        name=name,
        definition_id=f"test_{name.lower()}",
        is_player_side=is_player,
        rpg_level=1,
        star_level=0,
        max_hp=hp,
        current_hp=hp,
        max_mp=mp,
        current_mp=mp
    )
    
    # 添加基本技能
    basic_attack = SkillInstance(
        skill_id="basic_attack",
        skill_type=SkillType.PRIMARY_ATTACK,
        current_level=1
    )
    combatant.primary_attack_skill = basic_attack
    combatant.skill_order_preference = ["basic_attack"]
    
    return combatant


def test_battle_initialization():
    """測試戰鬥初始化"""
    print("=== 測試戰鬥初始化 ===")
    
    # 創建戰鬥單位
    player1 = create_test_combatant("玩家1", True, 100, 50)
    player2 = create_test_combatant("玩家2", True, 120, 40)
    monster1 = create_test_combatant("怪物1", False, 80, 30)
    
    # 創建戰鬥
    battle = Battle(
        player_team=[player1, player2],
        monster_team=[monster1]
    )
    
    # 檢查初始狀態
    assert battle.battle_status == BattleStatus.PENDING, "戰鬥狀態應該是PENDING"
    assert battle.current_turn == 0, "當前回合應該是0"
    assert len(battle.battle_log) == 0, "戰鬥日誌應該為空"
    
    print(f"✅ 戰鬥初始化成功，玩家隊伍: {len(battle.player_team)}, 怪物隊伍: {len(battle.monster_team)}")


def test_battle_start():
    """測試戰鬥開始"""
    print("\n=== 測試戰鬥開始 ===")
    
    # 創建戰鬥單位
    player1 = create_test_combatant("快速玩家", True, 100, 50)
    player1.current_stats['spd'] = 20.0  # 高速度
    
    monster1 = create_test_combatant("慢速怪物", False, 80, 30)
    monster1.current_stats['spd'] = 10.0  # 低速度
    
    # 創建戰鬥
    battle = Battle(
        player_team=[player1],
        monster_team=[monster1]
    )
    
    # 開始戰鬥
    battle.start()
    
    # 檢查戰鬥狀態
    assert battle.battle_status == BattleStatus.IN_PROGRESS, "戰鬥狀態應該是IN_PROGRESS"
    assert battle.current_turn == 1, "當前回合應該是1"
    assert len(battle.combatant_queue) == 2, "行動隊列應該有2個單位"
    
    # 檢查行動順序（速度高的先行動）
    first_actor = battle.get_acting_combatant()
    assert first_actor.name == "快速玩家", "第一個行動者應該是速度最快的"
    
    print(f"✅ 戰鬥開始成功，第一個行動者: {first_actor.name}")
    print(f"   行動隊列: {[battle.get_combatant_by_id(cid).name for cid in battle.combatant_queue]}")


def test_battle_action_processing():
    """測試戰鬥行動處理"""
    print("\n=== 測試戰鬥行動處理 ===")
    
    # 創建戰鬥單位
    player1 = create_test_combatant("攻擊者", True, 100, 50)
    monster1 = create_test_combatant("目標", False, 50, 30)
    
    # 創建戰鬥
    battle = Battle(
        player_team=[player1],
        monster_team=[monster1]
    )
    battle.start()
    
    # 測試無效技能
    result = battle.process_action(
        caster=player1,
        skill_id="invalid_skill"
    )
    assert not result['success'], "無效技能應該返回失敗"
    print(f"✅ 無效技能處理正確: {result['error']}")
    
    # 測試缺少依賴
    result = battle.process_action(
        caster=player1,
        skill_id="basic_attack"
    )
    assert not result['success'], "缺少依賴應該返回失敗"
    print(f"✅ 缺少依賴處理正確: {result['error']}")


def test_battle_win_condition():
    """測試戰鬥勝負條件"""
    print("\n=== 測試戰鬥勝負條件 ===")
    
    # 創建戰鬥單位
    player1 = create_test_combatant("玩家", True, 100, 50)
    monster1 = create_test_combatant("怪物", False, 1, 30)  # 低血量怪物
    
    # 創建戰鬥
    battle = Battle(
        player_team=[player1],
        monster_team=[monster1]
    )
    battle.start()
    
    # 模擬怪物死亡
    monster1.current_hp = 0
    
    # 檢查勝負條件
    battle.check_win_condition()
    
    assert battle.battle_status == BattleStatus.PLAYER_WIN, "玩家應該獲勝"
    print(f"✅ 勝負條件檢查正確: {battle.battle_status.value}")


def test_battle_log():
    """測試戰鬥日誌"""
    print("\n=== 測試戰鬥日誌 ===")
    
    # 創建戰鬥
    battle = Battle()
    
    # 添加日誌條目
    battle.add_log_entry(
        message="測試日誌",
        action_type="TEST",
        actor_id="test_actor",
        target_ids=["test_target"],
        details={"test": "data"}
    )
    
    assert len(battle.battle_log) == 1, "應該有一條日誌"
    log_entry = battle.battle_log[0]
    assert log_entry.message == "測試日誌", "日誌消息應該正確"
    assert log_entry.action_type == "TEST", "行動類型應該正確"
    
    print(f"✅ 戰鬥日誌功能正常: {log_entry.message}")


def test_combatant_targeting():
    """測試戰鬥單位目標選擇"""
    print("\n=== 測試戰鬥單位目標選擇 ===")
    
    # 創建戰鬥單位
    player1 = create_test_combatant("玩家1", True)
    player2 = create_test_combatant("玩家2", True)
    monster1 = create_test_combatant("怪物1", False)
    monster2 = create_test_combatant("怪物2", False)
    
    # 創建戰鬥
    battle = Battle(
        player_team=[player1, player2],
        monster_team=[monster1, monster2]
    )
    
    # 測試敵人選擇
    enemies = battle.get_all_alive_enemies_of(player1)
    assert len(enemies) == 2, "玩家應該有2個敵人"
    assert all(not e.is_player_side for e in enemies), "所有敵人都應該是怪物"
    
    # 測試盟友選擇
    allies = battle.get_all_alive_allies_of(player1)
    assert len(allies) == 2, "玩家應該有2個盟友（包括自己）"
    assert all(e.is_player_side for e in allies), "所有盟友都應該是玩家"
    
    print(f"✅ 目標選擇功能正常: 敵人{len(enemies)}個, 盟友{len(allies)}個")


def main():
    """主測試函數"""
    print("開始測試戰鬥系統...")
    
    try:
        test_battle_initialization()
        test_battle_start()
        test_battle_action_processing()
        test_battle_win_condition()
        test_battle_log()
        test_combatant_targeting()
        
        print("\n🎉 所有戰鬥系統測試通過！")
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    main()
