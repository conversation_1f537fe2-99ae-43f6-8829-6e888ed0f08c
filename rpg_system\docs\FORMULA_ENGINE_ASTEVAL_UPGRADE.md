# 公式求值引擎 asteval 升級報告

## 概述

根據設計文檔 `RPG_09_Formula_Engine_Details.md` 的建議，我們已經成功將 `FormulaEvaluator` 從使用 Python 的 `eval()` 升級為使用 `asteval` 庫，大幅提升了安全性和功能性。

## 升級內容

### 🔧 技術改進

#### 1. **安全性提升**
- **之前**: 使用 Python 的 `eval()` 函數，存在代碼注入風險
- **現在**: 使用 `asteval` 庫，提供沙盒化的安全執行環境
- **優勢**: 完全隔離危險操作，無法執行文件操作、網絡請求等

#### 2. **錯誤處理改進**
- **之前**: 簡單的 try-catch 錯誤處理
- **現在**: 
  - 檢查 `asteval` 的錯誤狀態
  - 檢查返回值是否為 None
  - 詳細的錯誤日誌記錄
  - 安全的降級處理（返回 0.0）

#### 3. **變量處理優化**
- **之前**: 手動字符串替換，容易出錯
- **現在**: 直接設置到 `asteval` 的符號表
- **特殊處理**: 自動將點號變量名轉換為下劃線（`caster.stat.patk` → `caster_stat_patk`）

### 🎯 功能擴展

#### 1. **數學函數庫**
```python
# 基礎數學函數
min(a, b)           # 最小值
max(a, b)           # 最大值
abs(x)              # 絕對值
round(x)            # 四捨五入
floor(x)            # 向下取整
ceil(x)             # 向上取整
sqrt(x)             # 平方根
pow(x, y)           # 冪運算
ln(x)               # 自然對數
log10(x)            # 常用對數
log(x, base)        # 指定底數的對數
```

#### 2. **邏輯函數**
```python
# 條件函數（避免與 Python if 關鍵字衝突）
iff(condition, value_if_true, value_if_false)

# 數值限制函數
clamp(value, min_val, max_val)

# 示例
iff(hp_percent < 0.5, 100, 50)           # 血量低於50%時返回100，否則50
clamp(damage * 2, 50, 200)               # 將傷害限制在50-200之間
```

#### 3. **數學常量**
```python
PI      # 圓周率 (3.141592653589793)
E       # 自然常數 (2.718281828459045)

# 示例
PI * 2              # 計算圓的周長係數
E ** x              # 指數函數
```

### 🧪 測試驗證

#### 測試覆蓋範圍
1. **基本數學運算**: `+`, `-`, `*`, `/`, `**`, `()`
2. **變量替換**: 支持複雜的變量名和上下文
3. **數學函數**: 所有內建數學函數
4. **邏輯函數**: 條件判斷和數值限制
5. **點號變量**: 自動轉換機制
6. **安全性**: 錯誤輸入的安全處理
7. **常量**: 數學常量的正確性

#### 測試結果
```
✅ 基本數學運算: 5/5 通過
✅ 變量替換: 4/4 通過  
✅ 數學函數: 8/8 通過
✅ 邏輯函數: 4/4 通過
✅ 點號變量處理: 3/3 通過
✅ 安全性測試: 5/5 通過
✅ 常量測試: 3/3 通過
```

## 使用示例

### 在配置文件中的應用

#### 傷害計算公式
```json
{
  "effect_type": "DAMAGE",
  "value_formula": "caster_stat_patk * 1.5 + skill_level * 10",
  "modifiers": [
    {
      "modifier_type": "CONDITIONAL_BOOST",
      "condition_formula": "target_current_hp_percent < 0.3",
      "boost_value": 0.5
    }
  ]
}
```

#### 治療計算公式
```json
{
  "effect_type": "HEAL",
  "value_formula": "iff(caster_current_hp_percent < 0.5, caster_stat_matk * 2, caster_stat_matk * 1.2)"
}
```

#### 被動觸發機率
```json
{
  "chance_formula": "clamp(caster_stat_crit_rate + skill_level * 0.05, 0.1, 0.8)"
}
```

### 在代碼中的使用

```python
from rpg_system.formula_engine.evaluator import get_formula_evaluator

evaluator = get_formula_evaluator()

# 基本計算
result = evaluator.evaluate("caster_stat_patk * 1.5", {
    'caster_stat_patk': 150
})  # 結果: 225.0

# 複雜邏輯
result = evaluator.evaluate(
    "iff(hp_percent < 0.5, max_heal, min_heal)", 
    {
        'hp_percent': 0.3,
        'max_heal': 100,
        'min_heal': 50
    }
)  # 結果: 100.0

# 點號變量自動轉換
result = evaluator.evaluate("caster_stat_patk * 2", {
    'caster.stat.patk': 200  # 自動轉換為 caster_stat_patk
})  # 結果: 400.0
```

## 向後兼容性

### 保持兼容的功能
- ✅ 所有現有的公式語法繼續工作
- ✅ 變量上下文傳遞方式不變
- ✅ 返回值類型保持一致（float）
- ✅ 錯誤處理行為一致（返回 0.0）

### 需要注意的變更
- ⚠️ `if` 函數改名為 `iff`（避免與 Python 關鍵字衝突）
- ⚠️ 點號變量名會自動轉換為下劃線
- ⚠️ 更嚴格的語法檢查（但這是好事）

## 性能影響

### 初始化成本
- **增加**: `asteval.Interpreter` 的創建和配置
- **優化**: 單例模式，只創建一次

### 運行時性能
- **輕微增加**: `asteval` 比原生 `eval()` 稍慢
- **可接受**: 對於遊戲邏輯計算來說性能影響微乎其微
- **安全收益**: 安全性提升遠超過微小的性能損失

## 未來擴展

### 可能的功能增強
1. **自定義函數**: 添加遊戲特定的計算函數
2. **變量驗證**: 在求值前驗證變量的有效性
3. **公式緩存**: 對常用公式進行編譯緩存
4. **調試模式**: 提供公式執行的詳細跟蹤

### 配置文件支持
- 可以在配置文件中定義自定義函數
- 支持更複雜的數學表達式
- 更好的錯誤提示和調試信息

## 總結

✅ **成功完成**: `FormulaEvaluator` 已成功升級為使用 `asteval`

🔒 **安全性**: 從潛在的安全風險升級為完全安全的沙盒執行

🚀 **功能性**: 從基礎計算升級為支持豐富數學和邏輯函數的強大引擎

🧪 **可靠性**: 通過全面測試，確保所有功能正常工作

📈 **可擴展性**: 為未來添加更多遊戲特定功能奠定了堅實基礎

這次升級為 RPG 系統提供了一個安全、強大、可擴展的公式計算引擎，完全符合設計文檔的要求和最佳實踐。
