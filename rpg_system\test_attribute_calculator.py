#!/usr/bin/env python3
"""
測試屬性計算器的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from battle_system.services.attribute_calculator import AttributeCalculator
from config.loader import ConfigLoader


class MockCardConfig:
    """模擬卡牌配置"""
    def __init__(self):
        self.base_stats = MockBaseStats()
        self.growth_per_rpg_level = MockGrowthConfig()
        self.star_level_effects_key = "test_star_effects"


class MockBaseStats:
    """模擬基礎屬性"""
    def __init__(self):
        self.max_hp = 100.0
        self.max_mp = 50.0
        self.mp_regen_per_turn = 0.0
        self.patk = 15.0
        self.pdef = 12.0
        self.matk = 18.0
        self.mdef = 14.0
        self.spd = 10.0
        self.crit_rate = 0.05
        self.crit_dmg_multiplier = 1.5
        self.accuracy = 0.95
        self.evasion = 0.05


class MockGrowthConfig:
    """模擬成長配置"""
    def __init__(self):
        self.hp = 5.0
        self.mp = 2.0
        self.patk = 1.5
        self.pdef = 1.0
        self.matk = 2.0
        self.mdef = 1.2
        self.spd = 0.8


class MockStarEffect:
    """模擬星級效果"""
    def __init__(self, required_star_level, flat_stats=None, percent_stats=None):
        self.required_star_level = required_star_level
        self.additional_stats_flat = flat_stats
        self.additional_stats_percent = percent_stats


class MockStarEffectsConfig:
    """模擬星級效果配置"""
    def __init__(self):
        self.effects = [
            MockStarEffect(5, flat_stats={'max_hp': 20.0, 'patk': 3.0}),
            MockStarEffect(10, percent_stats={'max_hp': 0.1, 'patk': 0.05}),
            MockStarEffect(15, flat_stats={'matk': 5.0}, percent_stats={'matk': 0.08})
        ]


class MockConfigLoader:
    """模擬配置加載器"""
    
    def get_card_config(self, card_id):
        if card_id == "test_card":
            return MockCardConfig()
        return None
    
    def get_monster_config(self, monster_id):
        if monster_id == "test_monster":
            return {
                'max_hp': 200.0,
                'max_mp': 0.0,
                'mp_regen_per_turn': 0.0,
                'patk': 25.0,
                'pdef': 20.0,
                'matk': 0.0,
                'mdef': 15.0,
                'spd': 8.0,
                'crit_rate': 0.03,
                'crit_dmg_multiplier': 1.3,
                'accuracy': 0.90,
                'evasion': 0.02
            }
        return None
    
    def get_star_level_effects_config(self, effects_key):
        if effects_key == "test_star_effects":
            return MockStarEffectsConfig()
        return None


def test_basic_card_attributes():
    """測試基礎卡牌屬性計算"""
    print("=== 測試基礎卡牌屬性計算 ===")
    
    calculator = AttributeCalculator()
    config_loader = MockConfigLoader()
    
    # 測試1級卡牌，無星級
    result = calculator.calculate_attributes(
        combatant_definition_id="test_card",
        combatant_type="CARD",
        rpg_level=1,
        star_level=0,
        config_loader=config_loader
    )
    
    print(f"1級卡牌屬性: {result}")
    
    # 驗證基礎屬性
    assert result['max_hp'] == 100.0, f"期望 max_hp=100.0, 實際={result['max_hp']}"
    assert result['patk'] == 15.0, f"期望 patk=15.0, 實際={result['patk']}"
    print("✅ 基礎屬性測試通過")


def test_rpg_level_growth():
    """測試RPG等級成長"""
    print("\n=== 測試RPG等級成長 ===")
    
    calculator = AttributeCalculator()
    config_loader = MockConfigLoader()
    
    # 測試10級卡牌，無星級
    result = calculator.calculate_attributes(
        combatant_definition_id="test_card",
        combatant_type="CARD",
        rpg_level=10,
        star_level=0,
        config_loader=config_loader
    )
    
    print(f"10級卡牌屬性: {result}")
    
    # 驗證成長計算 (9級成長)
    expected_hp = 100.0 + (5.0 * 9)  # 基礎100 + 9級成長
    expected_patk = 15.0 + (1.5 * 9)  # 基礎15 + 9級成長
    
    assert result['max_hp'] == expected_hp, f"期望 max_hp={expected_hp}, 實際={result['max_hp']}"
    assert result['patk'] == expected_patk, f"期望 patk={expected_patk}, 實際={result['patk']}"
    print("✅ RPG等級成長測試通過")


def test_star_level_effects():
    """測試星級效果"""
    print("\n=== 測試星級效果 ===")
    
    calculator = AttributeCalculator()
    config_loader = MockConfigLoader()
    
    # 測試1級卡牌，15星級
    result = calculator.calculate_attributes(
        combatant_definition_id="test_card",
        combatant_type="CARD",
        rpg_level=1,
        star_level=15,
        config_loader=config_loader
    )
    
    print(f"1級15星卡牌屬性: {result}")
    
    # 驗證星級效果
    # 5星: +20 HP, +3 patk
    # 10星: +10% HP (基於基礎100), +5% patk (基於基礎15)
    # 15星: +5 matk, +8% matk (基於基礎18)
    expected_hp = 100.0 + 20.0 + (100.0 * 0.1)  # 基礎 + 扁平 + 百分比
    expected_patk = 15.0 + 3.0 + (15.0 * 0.05)  # 基礎 + 扁平 + 百分比
    expected_matk = 18.0 + 5.0 + (18.0 * 0.08)  # 基礎 + 扁平 + 百分比
    
    assert abs(result['max_hp'] - expected_hp) < 0.01, f"期望 max_hp={expected_hp}, 實際={result['max_hp']}"
    assert abs(result['patk'] - expected_patk) < 0.01, f"期望 patk={expected_patk}, 實際={result['patk']}"
    assert abs(result['matk'] - expected_matk) < 0.01, f"期望 matk={expected_matk}, 實際={result['matk']}"
    print("✅ 星級效果測試通過")


def test_combined_effects():
    """測試組合效果"""
    print("\n=== 測試組合效果 ===")
    
    calculator = AttributeCalculator()
    config_loader = MockConfigLoader()
    
    # 測試10級15星卡牌
    result = calculator.calculate_attributes(
        combatant_definition_id="test_card",
        combatant_type="CARD",
        rpg_level=10,
        star_level=15,
        config_loader=config_loader
    )
    
    print(f"10級15星卡牌屬性: {result}")
    
    # 驗證組合效果
    # 基礎: 100 HP, 15 patk, 18 matk
    # 9級成長: +45 HP, +13.5 patk, +18 matk
    # 星級效果: +20 HP, +10% 基礎HP, +3 patk, +5% 基礎patk, +5 matk, +8% 基礎matk
    expected_hp = 100.0 + (5.0 * 9) + 20.0 + (100.0 * 0.1)
    expected_patk = 15.0 + (1.5 * 9) + 3.0 + (15.0 * 0.05)
    expected_matk = 18.0 + (2.0 * 9) + 5.0 + (18.0 * 0.08)
    
    print(f"期望 HP: {expected_hp}, 實際: {result['max_hp']}")
    print(f"期望 patk: {expected_patk}, 實際: {result['patk']}")
    print(f"期望 matk: {expected_matk}, 實際: {result['matk']}")
    
    assert abs(result['max_hp'] - expected_hp) < 0.01, f"期望 max_hp={expected_hp}, 實際={result['max_hp']}"
    assert abs(result['patk'] - expected_patk) < 0.01, f"期望 patk={expected_patk}, 實際={result['patk']}"
    assert abs(result['matk'] - expected_matk) < 0.01, f"期望 matk={expected_matk}, 實際={result['matk']}"
    print("✅ 組合效果測試通過")


def test_monster_attributes():
    """測試怪物屬性計算"""
    print("\n=== 測試怪物屬性計算 ===")
    
    calculator = AttributeCalculator()
    config_loader = MockConfigLoader()
    
    # 測試怪物屬性
    result = calculator.calculate_attributes(
        combatant_definition_id="test_monster",
        combatant_type="MONSTER",
        rpg_level=1,  # 怪物不受等級影響
        star_level=0,  # 怪物不受星級影響
        config_loader=config_loader
    )
    
    print(f"怪物屬性: {result}")
    
    # 驗證怪物屬性直接來自配置
    assert result['max_hp'] == 200.0, f"期望 max_hp=200.0, 實際={result['max_hp']}"
    assert result['patk'] == 25.0, f"期望 patk=25.0, 實際={result['patk']}"
    print("✅ 怪物屬性測試通過")


def main():
    """主測試函數"""
    print("開始測試屬性計算器...")
    
    try:
        test_basic_card_attributes()
        test_rpg_level_growth()
        test_star_level_effects()
        test_combined_effects()
        test_monster_attributes()
        
        print("\n🎉 所有測試通過！屬性計算器工作正常。")
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    main()
